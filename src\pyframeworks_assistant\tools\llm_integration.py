"""
LLM integration utilities for GAAPF.

This module provides integration with various LLM providers including
OpenAI GPT, Anthropic Claude, and Google Gemini.
"""

import os
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field

try:
    from langchain_openai import ChatOpenAI
    from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
    from langchain_google_genai import <PERSON><PERSON><PERSON><PERSON>gleGenerativeA<PERSON>
    from langchain_google_vertexai import ChatVertexAI
    from langchain_core.messages import HumanMessage, SystemMessage
    from langchain_core.tools import tool
    from langchain.agents import AgentExecutor, create_openai_functions_agent
    from langchain.agents.format_scratchpad import format_to_openai_function_messages
    from langchain.agents.output_parsers import OpenAIFunctionsAgentOutputParser
    from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE_GENAI = "google_genai"
    GOOGLE_VERTEX = "google_vertex"
    MOCK = "mock"


class GoogleAIService(str, Enum):
    """Google AI service types."""
    GENERATIVE_AI = "generative_ai"  # Google AI Studio / Generative AI API
    VERTEX_AI = "vertex_ai"          # Google Cloud Vertex AI


class LLMConfig(BaseModel):
    """Configuration for LLM integration."""

    provider: LLMProvider = Field(..., description="LLM provider")
    model_name: str = Field(..., description="Model name")
    api_key: Optional[str] = Field(None, description="API key")
    temperature: float = Field(0.7, description="Temperature setting")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens")

    # Google-specific configuration
    google_service: Optional[GoogleAIService] = Field(None, description="Google AI service type")
    project_id: Optional[str] = Field(None, description="Google Cloud project ID (for Vertex AI)")
    location: Optional[str] = Field("us-central1", description="Google Cloud location (for Vertex AI)")

    # LangChain Agent configuration
    enable_tools: bool = Field(True, description="Enable tool usage for agents")
    max_iterations: int = Field(10, description="Maximum agent iterations")

    class Config:
        use_enum_values = True


class LLMIntegration:
    """
    LLM integration manager for GAAPF.
    
    Provides unified interface for interacting with different LLM providers
    while handling authentication, rate limiting, and error handling.
    """
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize LLM integration."""
        self.config = config or self._auto_detect_config()
        self.client = self._initialize_client()
    
    def _auto_detect_config(self) -> LLMConfig:
        """Auto-detect available LLM configuration."""

        # Check for API keys in environment
        if os.getenv("OPENAI_API_KEY"):
            return LLMConfig(
                provider=LLMProvider.OPENAI,
                model_name="gpt-3.5-turbo",
                api_key=os.getenv("OPENAI_API_KEY")
            )
        elif os.getenv("ANTHROPIC_API_KEY"):
            return LLMConfig(
                provider=LLMProvider.ANTHROPIC,
                model_name="claude-3-sonnet-20240229",
                api_key=os.getenv("ANTHROPIC_API_KEY")
            )
        elif os.getenv("GOOGLE_API_KEY"):
            return LLMConfig(
                provider=LLMProvider.GOOGLE_GENAI,
                model_name="gemini-pro",
                api_key=os.getenv("GOOGLE_API_KEY"),
                google_service=GoogleAIService.GENERATIVE_AI
            )
        elif os.getenv("GOOGLE_APPLICATION_CREDENTIALS") and os.getenv("GOOGLE_CLOUD_PROJECT"):
            return LLMConfig(
                provider=LLMProvider.GOOGLE_VERTEX,
                model_name="gemini-pro",
                project_id=os.getenv("GOOGLE_CLOUD_PROJECT"),
                location=os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1"),
                google_service=GoogleAIService.VERTEX_AI
            )
        else:
            # Fall back to mock provider
            return LLMConfig(
                provider=LLMProvider.MOCK,
                model_name="mock-model"
            )
    
    def _initialize_client(self) -> Any:
        """Initialize the LLM client based on configuration."""
        
        if not LANGCHAIN_AVAILABLE:
            print("Warning: LangChain not available, using mock client")
            return None
        
        try:
            if self.config.provider == LLMProvider.OPENAI:
                return ChatOpenAI(
                    model=self.config.model_name,
                    api_key=self.config.api_key,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            elif self.config.provider == LLMProvider.ANTHROPIC:
                return ChatAnthropic(
                    model=self.config.model_name,
                    api_key=self.config.api_key,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            elif self.config.provider == LLMProvider.GOOGLE_GENAI:
                return ChatGoogleGenerativeAI(
                    model=self.config.model_name,
                    google_api_key=self.config.api_key,
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_tokens
                )
            elif self.config.provider == LLMProvider.GOOGLE_VERTEX:
                return ChatVertexAI(
                    model_name=self.config.model_name,
                    project=self.config.project_id,
                    location=self.config.location,
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_tokens
                )
            else:
                return None  # Mock client
                
        except Exception as e:
            print(f"Warning: Failed to initialize {self.config.provider} client: {e}")
            return None
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate response from LLM."""
        
        if not self.client:
            return self._generate_mock_response(messages)
        
        try:
            # Convert messages to LangChain format
            langchain_messages = []
            
            if system_prompt:
                langchain_messages.append(SystemMessage(content=system_prompt))
            
            for msg in messages:
                if msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))
            
            # Generate response
            response = await self.client.ainvoke(langchain_messages)
            return response.content
            
        except Exception as e:
            print(f"Error generating LLM response: {e}")
            return self._generate_mock_response(messages)
    
    def _generate_mock_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate mock response for testing/demo purposes."""
        
        last_message = messages[-1]["content"] if messages else ""
        
        # Simple keyword-based mock responses
        if "langchain" in last_message.lower():
            return """LangChain is a powerful framework for building applications with Large Language Models (LLMs). Here are the key concepts:

## Core Components:
1. **LLMs and Chat Models** - Interface with language models
2. **Prompts** - Templates for structuring inputs
3. **Chains** - Combine multiple components
4. **Agents** - Use LLMs to decide actions
5. **Memory** - Persist state between calls

## Basic Example:
```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate

llm = OpenAI(temperature=0.7)
prompt = PromptTemplate(
    input_variables=["topic"],
    template="Explain {topic} in simple terms"
)

chain = prompt | llm
result = chain.invoke({"topic": "machine learning"})
```

Would you like me to explain any of these concepts in more detail?"""
        
        elif "example" in last_message.lower() or "code" in last_message.lower():
            return """Here's a practical code example:

```python
# Simple LangChain example
from langchain.llms import OpenAI
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate

# Initialize the LLM
llm = OpenAI(temperature=0.7)

# Create a prompt template
prompt = PromptTemplate(
    input_variables=["question"],
    template="Answer this question clearly: {question}"
)

# Create a chain
chain = LLMChain(llm=llm, prompt=prompt)

# Use the chain
response = chain.run("What is artificial intelligence?")
print(response)
```

This example shows the basic pattern of LangChain: LLM + Prompt + Chain = Powerful Application!

Try running this code and let me know if you have questions!"""
        
        elif "practice" in last_message.lower() or "exercise" in last_message.lower():
            return """Great! Let's practice with some hands-on exercises:

## Exercise 1: Basic Chain
Create a simple chain that takes a programming concept and explains it with an analogy.

## Exercise 2: Prompt Engineering
Experiment with different prompt templates to see how they affect the output.

## Exercise 3: Memory Integration
Add conversation memory to maintain context across multiple interactions.

## Your Task:
Pick one exercise and try implementing it. I'll help you debug any issues you encounter!

Which exercise interests you most?"""
        
        else:
            return f"""I understand you're asking about: "{last_message}"

This is a great question! Let me help you learn about this topic step by step.

Based on your question, I recommend we explore:
1. The fundamental concepts involved
2. Practical examples and use cases  
3. Hands-on exercises to reinforce learning

Would you like me to start with an explanation, show you some code examples, or create a practice exercise?

I'm here to adapt to your learning style and help you master these concepts!"""
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current LLM provider."""
        
        return {
            "provider": self.config.provider,
            "model": self.config.model_name,
            "available": self.client is not None,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
        }
    
    def is_available(self) -> bool:
        """Check if LLM integration is available."""
        return self.client is not None or self.config.provider == LLMProvider.MOCK
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        # Very rough estimation: ~4 characters per token
        return len(text) // 4
    
    def validate_api_key(self) -> bool:
        """Validate API key configuration."""
        
        if self.config.provider == LLMProvider.MOCK:
            return True
        
        if not self.config.api_key:
            return False
        
        # Basic validation - check if key looks valid
        if self.config.provider == LLMProvider.OPENAI:
            return self.config.api_key.startswith("sk-")
        elif self.config.provider == LLMProvider.ANTHROPIC:
            return self.config.api_key.startswith("sk-ant-")
        elif self.config.provider == LLMProvider.GOOGLE_GENAI:
            return len(self.config.api_key) > 20
        elif self.config.provider == LLMProvider.GOOGLE_VERTEX:
            return bool(self.config.project_id)

        return True

    def create_agent_executor(self, tools: List[Any], system_prompt: str = "") -> Optional[Any]:
        """Create a LangChain agent executor with tools."""
        if not LANGCHAIN_AVAILABLE or not self.client:
            return None

        try:
            # Create prompt template for agent
            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt or "You are a helpful AI assistant."),
                ("user", "{input}"),
                MessagesPlaceholder(variable_name="agent_scratchpad"),
            ])

            # Create agent
            if self.config.provider == LLMProvider.OPENAI:
                agent = create_openai_functions_agent(self.client, tools, prompt)
                return AgentExecutor(agent=agent, tools=tools, verbose=True, max_iterations=self.config.max_iterations)
            else:
                # For non-OpenAI providers, use a basic agent setup
                # This is a simplified approach - in production you might want provider-specific agents
                from langchain.agents import create_react_agent
                agent = create_react_agent(self.client, tools, prompt)
                return AgentExecutor(agent=agent, tools=tools, verbose=True, max_iterations=self.config.max_iterations)

        except Exception as e:
            print(f"Warning: Failed to create agent executor: {e}")
            return None

    def switch_provider(self, new_config: LLMConfig) -> bool:
        """Switch to a different LLM provider configuration."""
        try:
            old_config = self.config
            self.config = new_config
            new_client = self._initialize_client()

            if new_client or new_config.provider == LLMProvider.MOCK:
                self.client = new_client
                return True
            else:
                # Revert on failure
                self.config = old_config
                return False
        except Exception as e:
            print(f"Error switching provider: {e}")
            return False


# LangChain Tool Definitions
def get_framework_documentation(framework: str, topic: str) -> str:
    """
    Get documentation for a specific framework topic.

    Args:
        framework: The framework name (langchain, langgraph, etc.)
        topic: The specific topic to get documentation for

    Returns:
        Documentation content for the specified topic
    """
    # This would typically fetch from actual documentation sources
    # For now, return structured mock documentation
    return f"""
# {framework.title()} Documentation: {topic.title()}

## Overview
{topic.title()} is a key concept in {framework.title()} that enables...

## Key Features
- Feature 1: Description
- Feature 2: Description
- Feature 3: Description

## Usage Example
```python
# Example code for {topic} in {framework}
from {framework} import {topic.title()}

# Basic usage
example = {topic.title()}()
result = example.process()
```

## Best Practices
1. Always initialize properly
2. Handle errors gracefully
3. Use appropriate configuration

## Related Topics
- Related Topic 1
- Related Topic 2
"""


def analyze_code_complexity(code: str) -> str:
    """
    Analyze the complexity of provided code.

    Args:
        code: The code to analyze

    Returns:
        Analysis of code complexity and suggestions
    """
    lines = code.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]

    complexity_score = len(non_empty_lines)
    if complexity_score < 10:
        level = "Simple"
    elif complexity_score < 50:
        level = "Moderate"
    else:
        level = "Complex"

    return f"""
# Code Complexity Analysis

## Metrics
- Total lines: {len(lines)}
- Non-empty lines: {len(non_empty_lines)}
- Complexity level: {level}

## Suggestions
- Consider breaking down large functions
- Add comments for complex logic
- Use meaningful variable names
- Follow PEP 8 style guidelines
"""


def generate_learning_exercise(topic: str, difficulty: str = "intermediate") -> str:
    """
    Generate a learning exercise for a specific topic.

    Args:
        topic: The topic to create an exercise for
        difficulty: The difficulty level (beginner, intermediate, advanced)

    Returns:
        A structured learning exercise
    """
    return f"""
# Learning Exercise: {topic.title()}

## Difficulty Level: {difficulty.title()}

## Objective
Master the fundamentals of {topic} through hands-on practice.

## Exercise Tasks
1. **Setup**: Initialize a new project with {topic}
2. **Implementation**: Create a basic example using {topic}
3. **Enhancement**: Add advanced features
4. **Testing**: Verify your implementation works correctly

## Expected Outcome
By completing this exercise, you will understand:
- Core concepts of {topic}
- Practical implementation patterns
- Common pitfalls and how to avoid them

## Next Steps
- Try variations of the exercise
- Explore advanced features
- Build a small project using {topic}
"""


# Available tools for agents
AVAILABLE_TOOLS = [
    get_framework_documentation,
    analyze_code_complexity,
    generate_learning_exercise,
]
