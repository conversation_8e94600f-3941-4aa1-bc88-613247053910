#!/usr/bin/env python3
"""
GAAPF Web Interface Launcher

This script launches the Streamlit web interface for GAAPF.
"""

import sys
import subprocess
from pathlib import Path

def main():
    """Launch the Streamlit web interface."""
    
    print("🌐 Starting GAAPF Web Interface...")
    print("=" * 50)
    
    # Add src to Python path
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    # Path to the Streamlit app
    app_path = "src/pyframeworks_assistant/interfaces/web/streamlit_app.py"
    
    if not Path(app_path).exists():
        print(f"❌ Error: Streamlit app not found at {app_path}")
        print("Please ensure you're running this from the GAAPF root directory.")
        sys.exit(1)
    
    try:
        # Launch Streamlit
        print(f"🚀 Launching Streamlit app: {app_path}")
        print("📱 The web interface will open in your browser automatically")
        print("🔗 URL: http://localhost:8501")
        print()
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", app_path,
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n\n👋 Web interface stopped by user")
    except FileNotFoundError:
        print("❌ Error: Streamlit not found")
        print("Please install Streamlit: pip install streamlit")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error launching web interface: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
