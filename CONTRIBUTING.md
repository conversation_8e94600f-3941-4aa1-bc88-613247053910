# Contributing to GAAPF

Thank you for your interest in contributing to GAAPF (Guidance AI Agent for Python Framework)! This document provides guidelines and information for contributors.

## 🌟 Ways to Contribute

### 🐛 Bug Reports
- Use the GitHub issue tracker
- Provide detailed reproduction steps
- Include system information and error messages
- Check if the issue already exists

### ✨ Feature Requests
- Describe the feature and its use case
- Explain how it fits with GAAPF's goals
- Consider implementation complexity
- Discuss with maintainers before large changes

### 📝 Documentation
- Improve existing documentation
- Add examples and tutorials
- Fix typos and clarify explanations
- Translate documentation

### 💻 Code Contributions
- Bug fixes
- New features
- Performance improvements
- Test coverage improvements

## 🚀 Getting Started

### Prerequisites
- Python 3.10 or higher
- Git
- At least one LLM API key (Google Gemini recommended for free tier)

### Development Setup

1. **Fork and clone the repository**
```bash
git clone https://github.com/your-username/gaapf-guidance-ai-agent.git
cd gaapf-guidance-ai-agent
```

2. **Create a virtual environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**
```bash
pip install -r requirements.txt
pip install -e .  # Install in development mode
```

4. **Set up environment**
```bash
cp .env.example .env
# Edit .env with your API keys
```

5. **Run tests**
```bash
python test_basic.py
pytest  # If you have pytest installed
```

## 📋 Development Guidelines

### Code Style
- Follow PEP 8 Python style guidelines
- Use type hints for all functions and methods
- Write docstrings for all public functions and classes
- Use meaningful variable and function names

### Code Formatting
We use automated formatting tools:

```bash
# Format code with Black
black src/ test_basic.py

# Lint with Ruff
ruff check src/

# Type checking with MyPy
mypy src/
```

### Testing
- Write tests for new features
- Ensure existing tests pass
- Aim for good test coverage
- Test both success and error cases

```bash
# Run basic tests
python test_basic.py

# Run with pytest (if available)
pytest tests/

# Test specific components
python -m pytest tests/test_agents.py
```

## 🏗️ Architecture Overview

### Core Components
- **Agents**: Specialized AI agents for different learning domains
- **Constellations**: Adaptive agent teams that form based on user needs
- **Temporal State**: Learning optimization and pattern recognition
- **Memory**: Session and user memory management
- **Interfaces**: CLI, Web, and API interfaces

### Key Principles
- **Modularity**: Components should be loosely coupled
- **Extensibility**: Easy to add new agents, constellations, and frameworks
- **Type Safety**: Use Pydantic models and type hints
- **Async Support**: Use async/await for I/O operations
- **Error Handling**: Graceful degradation and informative error messages

## 📝 Contribution Process

### 1. Planning
- **Small changes**: Create an issue or start working directly
- **Large changes**: Discuss in an issue first
- **New features**: Ensure they align with project goals

### 2. Development
- Create a feature branch: `git checkout -b feature/your-feature-name`
- Make your changes following the guidelines
- Write or update tests
- Update documentation if needed

### 3. Testing
- Run the test suite: `python test_basic.py`
- Test your changes manually
- Ensure no regressions

### 4. Documentation
- Update relevant documentation
- Add docstrings to new functions
- Update README.md if needed
- Consider adding examples

### 5. Submission
- Commit your changes with clear messages
- Push to your fork
- Create a pull request
- Respond to review feedback

## 🔍 Pull Request Guidelines

### PR Title and Description
- Use clear, descriptive titles
- Explain what the PR does and why
- Reference related issues
- Include screenshots for UI changes

### PR Checklist
- [ ] Code follows style guidelines
- [ ] Tests pass
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
- [ ] Commit messages are clear

### Review Process
- Maintainers will review your PR
- Address feedback promptly
- Be open to suggestions
- Squash commits if requested

## 🧪 Testing Guidelines

### Test Structure
```
tests/
├── test_agents.py          # Agent functionality tests
├── test_constellations.py  # Constellation tests
├── test_temporal.py        # Temporal optimization tests
├── test_memory.py          # Memory management tests
└── test_interfaces.py      # Interface tests
```

### Writing Tests
```python
import pytest
from pyframeworks_assistant.agents import InstructorAgent

def test_instructor_agent_creation():
    """Test instructor agent can be created."""
    agent = InstructorAgent(llm_client=None)
    assert agent.agent_role.value == "instructor"

@pytest.mark.asyncio
async def test_agent_response():
    """Test agent generates responses."""
    agent = InstructorAgent(llm_client=None)
    # Test implementation
```

## 📚 Adding New Features

### Adding a New Agent
1. Create agent class in appropriate domain module
2. Inherit from `BaseAgent`
3. Implement required methods
4. Add to agent factory in constellation manager
5. Write tests
6. Update documentation

### Adding a New Constellation
1. Define constellation type in `constellation_configs.py`
2. Add configuration with agent composition
3. Update constellation selection logic
4. Test with different user profiles
5. Document the new constellation

### Adding a New Framework
1. Create framework configuration
2. Define learning modules
3. Add to supported frameworks
4. Create framework-specific content
5. Test integration
6. Update documentation

## 🐛 Debugging

### Common Issues
- **Import errors**: Check Python path and dependencies
- **API key errors**: Verify .env configuration
- **Test failures**: Check for environment differences

### Debugging Tools
- Use Python debugger: `import pdb; pdb.set_trace()`
- Enable verbose logging: `LOG_LEVEL=DEBUG`
- Check LangSmith traces if configured

## 📖 Documentation Standards

### Code Documentation
- Use Google-style docstrings
- Include parameter and return type information
- Provide usage examples for complex functions

```python
def example_function(param1: str, param2: int = 10) -> bool:
    """
    Example function with proper documentation.
    
    Args:
        param1: Description of the first parameter
        param2: Description of the second parameter with default value
        
    Returns:
        Boolean indicating success or failure
        
    Raises:
        ValueError: If param1 is empty
        
    Example:
        >>> result = example_function("test", 5)
        >>> print(result)
        True
    """
    if not param1:
        raise ValueError("param1 cannot be empty")
    return len(param1) > param2
```

### README Updates
- Keep installation instructions current
- Update feature lists
- Add new interface documentation
- Include troubleshooting for common issues

## 🤝 Community Guidelines

### Code of Conduct
- Be respectful and inclusive
- Welcome newcomers
- Provide constructive feedback
- Focus on the code, not the person

### Communication
- Use GitHub issues for bug reports and feature requests
- Use GitHub discussions for questions and ideas
- Be patient with response times
- Provide context and details

## 🏷️ Release Process

### Versioning
We follow semantic versioning (SemVer):
- **Major**: Breaking changes
- **Minor**: New features, backward compatible
- **Patch**: Bug fixes, backward compatible

### Release Checklist
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Version number bumped
- [ ] Changelog updated
- [ ] Release notes prepared

## 📞 Getting Help

### Resources
- **Documentation**: README.md, CLI_GUIDE.md, API_DOCUMENTATION.md
- **Issues**: GitHub issue tracker
- **Discussions**: GitHub discussions
- **Code**: Browse the source code with comments

### Contact
- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and ideas
- **Email**: <EMAIL> (if available)

---

Thank you for contributing to GAAPF! Your contributions help make AI education more accessible and effective for everyone. 🚀
