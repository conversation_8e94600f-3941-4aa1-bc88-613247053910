"""
Command-line interface for GAAPF with real LLM integration.

This module provides the main CLI application that offers the complete
GAAPF experience with actual AI responses and adaptive learning.
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

import typer
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.markdown import Markdown
from rich.progress import Progress, SpinnerColumn, TextColumn
from dotenv import load_dotenv

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from pyframeworks_assistant.config.user_profiles import (
    UserProfile, SkillLevel, LearningPace, LearningStyle,
    DifficultyProgression, FeedbackStyle
)
from pyframeworks_assistant.config.framework_configs import (
    SupportedFrameworks, get_framework_config, get_available_frameworks
)
from pyframeworks_assistant.config.constellation_configs import (
    ConstellationType, get_available_constellations
)
from pyframeworks_assistant.core.constellation import ConstellationManager
from pyframeworks_assistant.core.temporal_state import TemporalStateManager
from pyframeworks_assistant.core.learning_hub import LearningHub
from pyframeworks_assistant.core.adaptive_engine import AdaptiveEngine

# Load environment variables
load_dotenv()

app = typer.Typer(help="GAAPF - Guidance AI Agent for Python Framework")
console = Console()


class CLIState:
    """Manages CLI application state."""
    
    def __init__(self):
        self.user_profile: Optional[UserProfile] = None
        self.current_session_id: Optional[str] = None
        self.learning_hub: Optional[LearningHub] = None
        self.adaptive_engine: Optional[AdaptiveEngine] = None
        self.profiles_dir = Path("data/user_profiles")
        self.profiles_dir.mkdir(parents=True, exist_ok=True)
    
    async def initialize_system(self):
        """Initialize the GAAPF system components."""

        # Initialize LLM integration
        from pyframeworks_assistant.tools.llm_integration import LLMIntegration
        llm_integration = LLMIntegration()

        # Display LLM provider info
        provider_info = llm_integration.get_provider_info()
        console.print(f"🤖 LLM Provider: {provider_info['provider'].title()}")
        console.print(f"📋 Model: {provider_info['model']}")
        console.print(f"✅ Available: {'Yes' if provider_info['available'] else 'No (using mock responses)'}")

        # Initialize core components
        constellation_manager = ConstellationManager(llm_integration)
        temporal_manager = TemporalStateManager()

        self.learning_hub = LearningHub(constellation_manager, temporal_manager)
        self.adaptive_engine = AdaptiveEngine(temporal_manager, constellation_manager)
    
    def save_user_profile(self, profile: UserProfile):
        """Save user profile to disk."""
        profile_file = self.profiles_dir / f"{profile.user_id}.json"
        with open(profile_file, 'w') as f:
            json.dump(profile.dict(), f, indent=2, default=str)
    
    def load_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Load user profile from disk."""
        profile_file = self.profiles_dir / f"{user_id}.json"
        if profile_file.exists():
            with open(profile_file, 'r') as f:
                data = json.load(f)
                return UserProfile(**data)
        return None
    
    def list_user_profiles(self) -> list:
        """List available user profiles."""
        profiles = []
        for profile_file in self.profiles_dir.glob("*.json"):
            try:
                with open(profile_file, 'r') as f:
                    data = json.load(f)
                    profiles.append({
                        "user_id": data.get("user_id"),
                        "name": data.get("name", "Unknown"),
                        "created_at": data.get("created_at"),
                    })
            except Exception:
                continue
        return profiles


cli_state = CLIState()


def display_welcome():
    """Display welcome message and system info."""
    
    welcome_text = """
# 🤖 GAAPF - Guidance AI Agent for Python Framework

**An Adaptive Multi-Agent Learning System for AI Framework Education**

Welcome to your personalized AI learning companion! GAAPF uses advanced 
constellation-based agent systems to provide adaptive, intelligent guidance 
for learning Python AI frameworks like LangChain and LangGraph.

## ✨ What makes GAAPF special:
- **Adaptive Learning Constellations**: Dynamic agent teams that adapt to your learning style
- **Temporal Optimization**: Learns from your patterns to improve over time  
- **Real AI Integration**: Actual LLM responses, not mock data
- **Personalized Experience**: Tailored to your skill level and goals

Ready to start your AI learning journey? Let's begin! 🚀
"""
    
    console.print(Panel(Markdown(welcome_text), title="Welcome to GAAPF", border_style="blue"))


def create_user_profile() -> UserProfile:
    """Interactive user profile creation."""
    
    console.print("\n[bold blue]Let's create your learning profile![/bold blue]\n")
    
    # Basic information
    name = Prompt.ask("What's your name?", default="Learner")
    user_id = Prompt.ask("Choose a username", default=name.lower().replace(" ", "_"))
    
    # Check if profile exists
    existing_profile = cli_state.load_user_profile(user_id)
    if existing_profile:
        if Confirm.ask(f"Profile '{user_id}' already exists. Load existing profile?"):
            return existing_profile
    
    # Programming experience
    console.print("\n[bold]Programming Experience[/bold]")
    years = typer.prompt("How many years of programming experience do you have?", type=int, default=0)
    
    # Python skill level
    console.print("\n[bold]Python Skill Level[/bold]")
    skill_options = [level.value for level in SkillLevel]
    console.print(f"Options: {', '.join(skill_options)}")
    python_skill = Prompt.ask("Your Python skill level", choices=skill_options, default="beginner")
    
    # AI/ML experience
    console.print("\n[bold]AI/ML Experience[/bold]")
    ai_skill = Prompt.ask("Your AI/ML experience level", choices=skill_options, default="none")
    
    # Learning preferences
    console.print("\n[bold]Learning Preferences[/bold]")
    
    pace_options = [pace.value for pace in LearningPace]
    console.print(f"Learning pace options: {', '.join(pace_options)}")
    learning_pace = Prompt.ask("Preferred learning pace", choices=pace_options, default="moderate")
    
    style_options = [style.value for style in LearningStyle]
    console.print(f"Learning style options: {', '.join(style_options)}")
    learning_style = Prompt.ask("Preferred learning style", choices=style_options, default="mixed")
    
    # Learning goals
    console.print("\n[bold]Learning Goals[/bold]")
    goals_input = Prompt.ask("What are your learning goals? (comma-separated)", default="Learn AI frameworks")
    learning_goals = [goal.strip() for goal in goals_input.split(",")]
    
    # Create profile
    profile = UserProfile(
        user_id=user_id,
        name=name,
        programming_experience_years=years,
        python_skill_level=SkillLevel(python_skill),
        ai_ml_experience=SkillLevel(ai_skill),
        learning_pace=LearningPace(learning_pace),
        preferred_learning_style=LearningStyle(learning_style),
        learning_goals=learning_goals,
    )
    
    # Save profile
    cli_state.save_user_profile(profile)
    
    console.print(f"\n[green]✅ Profile created successfully for {name}![/green]")
    return profile


def select_framework() -> SupportedFrameworks:
    """Framework selection interface."""
    
    console.print("\n[bold blue]Choose a Framework to Learn[/bold blue]\n")
    
    available_frameworks = get_available_frameworks()
    
    # Display framework options
    table = Table(title="Available Frameworks")
    table.add_column("Option", style="cyan", no_wrap=True)
    table.add_column("Framework", style="magenta")
    table.add_column("Description", style="green")
    table.add_column("Status", style="yellow")
    
    for i, framework in enumerate(available_frameworks, 1):
        config = get_framework_config(framework)
        table.add_row(
            str(i),
            config.display_name,
            config.description[:60] + "..." if len(config.description) > 60 else config.description,
            "✅ Ready" if config.is_fully_supported else "🔄 Coming Soon"
        )
    
    console.print(table)
    
    # Get user choice
    choice = typer.prompt(
        f"Select framework (1-{len(available_frameworks)})",
        type=int,
        default=1
    )
    
    if 1 <= choice <= len(available_frameworks):
        selected_framework = available_frameworks[choice - 1]
        config = get_framework_config(selected_framework)
        
        console.print(f"\n[green]✅ Selected: {config.display_name}[/green]")
        console.print(f"📚 Modules available: {len(config.modules)}")
        console.print(f"⏱️ Estimated time: {config.total_estimated_hours} hours")
        
        return selected_framework
    else:
        console.print("[red]Invalid choice. Defaulting to LangChain.[/red]")
        return SupportedFrameworks.LANGCHAIN


async def start_learning_session(profile: UserProfile, framework: SupportedFrameworks):
    """Start an interactive learning session."""
    
    console.print(f"\n[bold blue]Starting Learning Session[/bold blue]")
    console.print(f"👤 User: {profile.name}")
    console.print(f"📚 Framework: {framework.value.title()}")
    
    # Initialize session
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Initializing learning session...", total=None)
        
        session_info = await cli_state.learning_hub.start_learning_session(
            profile, framework
        )
        
        progress.update(task, description="Session ready!")
    
    cli_state.current_session_id = session_info["session_id"]
    
    # Display session info
    console.print(f"\n[green]✅ Session Started![/green]")
    console.print(f"🆔 Session ID: {session_info['session_id']}")
    console.print(f"⭐ Constellation: {session_info['constellation_type'].replace('_', ' ').title()}")
    console.print(f"🤖 Primary Agent: {session_info['primary_agent'].replace('_', ' ').title()}")
    
    # Display module info
    module_info = session_info["module_info"]
    console.print(f"\n📖 Current Module: {module_info['title']}")
    console.print(f"📝 Description: {module_info['description']}")
    
    # Display welcome message
    console.print(Panel(session_info["welcome_message"], title="Your AI Learning Assistant", border_style="green"))
    
    # Start interactive loop
    await interactive_learning_loop(profile)


async def interactive_learning_loop(profile: UserProfile):
    """Main interactive learning loop."""
    
    console.print("\n[bold]💬 Interactive Learning Session[/bold]")
    console.print("[dim]Type 'help' for commands, 'quit' to end session[/dim]\n")
    
    while True:
        try:
            # Get user input
            user_input = Prompt.ask("[bold blue]You[/bold blue]")
            
            # Handle special commands
            if user_input.lower() in ['quit', 'exit', 'bye']:
                break
            elif user_input.lower() == 'help':
                show_help()
                continue
            elif user_input.lower() == 'status':
                await show_session_status()
                continue
            elif user_input.lower() == 'adapt':
                await show_adaptations(profile)
                continue
            
            # Process learning interaction
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                task = progress.add_task("Processing your question...", total=None)
                
                response_data = await cli_state.learning_hub.process_learning_interaction(
                    cli_state.current_session_id, user_input, profile
                )
                
                progress.update(task, description="Response ready!")
            
            # Display agent response
            response = response_data["response"]
            agent_name = response["agent_role"].replace("_", " ").title()
            
            console.print(f"\n[bold green]🤖 {agent_name}[/bold green]")
            console.print(Panel(response["content"], border_style="green"))
            
            # Show learning suggestions if available
            suggestions = response_data.get("learning_suggestions", [])
            if suggestions:
                console.print("\n[bold yellow]💡 Learning Suggestions:[/bold yellow]")
                for i, suggestion in enumerate(suggestions, 1):
                    console.print(f"  {i}. {suggestion}")
            
            # Show session info
            session_info = response_data["session_info"]
            console.print(f"\n[dim]Session: {session_info['interactions']} interactions, "
                         f"{session_info['duration_minutes']:.1f} minutes[/dim]")
            
        except KeyboardInterrupt:
            console.print("\n[yellow]Session interrupted by user[/yellow]")
            break
        except Exception as e:
            console.print(f"\n[red]Error: {e}[/red]")
            continue
    
    # End session
    await end_learning_session()


async def end_learning_session():
    """End the current learning session."""
    
    if not cli_state.current_session_id:
        console.print("[yellow]No active session to end[/yellow]")
        return
    
    console.print("\n[bold blue]Ending Learning Session[/bold blue]")
    
    # Get user feedback
    feedback = {}
    if Confirm.ask("Would you like to provide feedback on this session?"):
        feedback["satisfaction"] = float(Prompt.ask("Rate your satisfaction (0.0-1.0)", default="0.8"))
        feedback["comprehension"] = float(Prompt.ask("Rate your comprehension (0.0-1.0)", default="0.7"))
    
    # End session
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Saving session data...", total=None)
        
        summary = await cli_state.learning_hub.end_learning_session(
            cli_state.current_session_id, feedback
        )
        
        progress.update(task, description="Session saved!")
    
    # Display session summary
    console.print("\n[green]✅ Session Completed![/green]")
    
    session_summary = summary["session_summary"]
    console.print(f"⏱️ Duration: {session_summary['duration_minutes']} minutes")
    console.print(f"💬 Interactions: {session_summary['interactions']}")
    console.print(f"📊 Effectiveness: {session_summary['effectiveness_score']:.1%}")
    console.print(f"🔄 Agent Handoffs: {session_summary['handoffs']}")
    
    # Display learning progress
    progress_info = summary["learning_progress"]
    console.print(f"\n📈 Learning Progress:")
    console.print(f"✅ Module Completed: {progress_info['module_completed']}")
    console.print(f"📊 Overall Progress: {progress_info['total_completion']:.1f}%")
    console.print(f"⏰ Total Hours: {progress_info['hours_invested']}")
    
    if progress_info['next_module']:
        console.print(f"➡️ Next Module: {progress_info['next_module']}")
    
    # Display recommendations
    recommendations = summary["recommendations"]
    if recommendations:
        console.print(f"\n[bold yellow]💡 Recommendations:[/bold yellow]")
        for rec in recommendations:
            console.print(f"  • {rec}")
    
    cli_state.current_session_id = None


def show_help():
    """Display help information."""
    
    help_text = """
## 🆘 Available Commands

**Learning Commands:**
- Just type your question or request naturally
- Ask for examples, explanations, or practice exercises
- Request specific topics or concepts

**Special Commands:**
- `help` - Show this help message
- `status` - Show current session status
- `adapt` - Show adaptation recommendations
- `quit` / `exit` / `bye` - End the session

**Tips:**
- Be specific about what you want to learn
- Ask for code examples when you need them
- Request practice exercises to test your knowledge
- The AI will automatically hand off to the best agent for your needs
"""
    
    console.print(Panel(Markdown(help_text), title="Help", border_style="yellow"))


async def show_session_status():
    """Show current session status."""
    
    if not cli_state.current_session_id:
        console.print("[yellow]No active session[/yellow]")
        return
    
    # This would get actual status from constellation manager
    console.print(f"\n[bold]📊 Session Status[/bold]")
    console.print(f"🆔 Session ID: {cli_state.current_session_id}")
    console.print(f"⏰ Duration: Active")
    console.print(f"🤖 Current Agent: Active")
    console.print(f"⭐ Constellation: Active")


async def show_adaptations(profile: UserProfile):
    """Show adaptation recommendations."""
    
    console.print(f"\n[bold]🔧 Adaptive Learning Insights[/bold]")
    
    # Get adaptation summary
    summary = cli_state.adaptive_engine.get_adaptation_summary(profile.user_id)
    
    if summary.get("message"):
        console.print("[yellow]No adaptation data available yet[/yellow]")
        return
    
    console.print(f"⭐ Current Constellation: {summary['current_constellation'].replace('_', ' ').title()}")
    console.print(f"📈 Learning Momentum: {summary['learning_momentum'].title()}")
    console.print(f"🎯 Consistency Score: {summary['consistency_score']:.1%}")
    
    if summary['recent_adaptations']:
        console.print(f"🔄 Recent Adaptations: {', '.join(summary['recent_adaptations'])}")


@app.command()
def run():
    """Run the GAAPF CLI application."""
    asyncio.run(main())


async def main():
    """Main CLI application entry point."""
    
    # Display welcome
    display_welcome()
    
    # Initialize system
    console.print("\n[bold]Initializing GAAPF system...[/bold]")
    await cli_state.initialize_system()
    
    # User profile management
    profiles = cli_state.list_user_profiles()
    
    if profiles and Confirm.ask("Load existing user profile?"):
        # Show available profiles
        table = Table(title="Available Profiles")
        table.add_column("Option", style="cyan")
        table.add_column("Username", style="magenta")
        table.add_column("Name", style="green")
        table.add_column("Created", style="yellow")
        
        for i, profile_info in enumerate(profiles, 1):
            table.add_row(
                str(i),
                profile_info["user_id"],
                profile_info["name"],
                profile_info["created_at"][:10] if profile_info["created_at"] else "Unknown"
            )
        
        console.print(table)
        
        choice = typer.prompt(f"Select profile (1-{len(profiles)})", type=int, default=1)
        if 1 <= choice <= len(profiles):
            selected_profile = profiles[choice - 1]
            cli_state.user_profile = cli_state.load_user_profile(selected_profile["user_id"])
        else:
            cli_state.user_profile = create_user_profile()
    else:
        cli_state.user_profile = create_user_profile()
    
    # Framework selection
    framework = select_framework()
    
    # Start learning session
    await start_learning_session(cli_state.user_profile, framework)
    
    console.print("\n[bold green]Thank you for using GAAPF! Happy learning! 🚀[/bold green]")


if __name__ == "__main__":
    app()
