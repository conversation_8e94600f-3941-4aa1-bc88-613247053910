"""
Constellation management system for adaptive agent teams.

This module implements the core constellation formation and management logic,
including agent selection, coordination, and intelligent handoff mechanisms.
Uses LangGraph for workflow orchestration and state management.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple, TypedDict
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

try:
    from langgraph.graph import StateGraph, END
    from langgraph.prebuilt import ToolExecutor
    from langgraph.checkpoint.sqlite import SqliteSaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import (
    ConstellationType, 
    ConstellationConfig, 
    AgentRole,
    get_constellation_config,
    get_optimal_constellation_for_profile
)
from ..agents.base_agent import BaseAgent, AgentResponse, HandoffSuggestion
from ..agents.knowledge_agents import (
    InstructorAgent, DocumentationExpertAgent, 
    ResearchAssistantAgent, KnowledgeSynthesizerAgent
)
from ..agents.practice_agents import (
    CodeAssistantAgent, PracticeFacilitatorAgent,
    ProjectGuideAgent, TroubleshooterAgent
)
from ..agents.support_agents import MentorAgent, MotivationalCoachAgent
from ..agents.assessment_agents import AssessmentAgent, ProgressTrackerAgent
from ..tools import AVAILABLE_TOOLS, get_available_tools, get_available_tools


# LangGraph State Definition
class ConstellationState(TypedDict):
    """State for LangGraph workflow."""
    user_message: str
    user_profile: Dict[str, Any]
    framework: str
    module_id: str
    session_context: Dict[str, Any]
    current_agent: str
    agent_response: Optional[str]
    handoff_suggestion: Optional[Dict[str, Any]]
    conversation_history: List[Dict[str, Any]]
    tools_used: List[str]
    iteration_count: int


class ConstellationStatus(str, Enum):
    """Status of a constellation."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    TRANSITIONING = "transitioning"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


class ActiveConstellation(BaseModel):
    """Represents an active agent constellation."""
    
    constellation_id: str = Field(..., description="Unique constellation identifier")
    constellation_type: ConstellationType = Field(..., description="Type of constellation")
    session_id: str = Field(..., description="Associated session ID")
    user_id: str = Field(..., description="User ID")
    
    # Agent Management
    active_agents: Dict[AgentRole, BaseAgent] = Field(default_factory=dict, description="Active agents")
    primary_agent: Optional[AgentRole] = Field(None, description="Currently primary agent")
    
    # Configuration
    config: ConstellationConfig = Field(..., description="Constellation configuration")
    max_concurrent_agents: int = Field(3, description="Maximum concurrent agents")
    
    # State Management
    status: ConstellationStatus = Field(ConstellationStatus.INITIALIZING, description="Constellation status")
    context_data: Dict[str, Any] = Field(default_factory=dict, description="Shared context data")
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list, description="Conversation history")
    
    # Metrics
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity_at: datetime = Field(default_factory=datetime.utcnow)
    total_interactions: int = Field(0, description="Total user interactions")
    handoff_count: int = Field(0, description="Number of agent handoffs")
    
    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity_at = datetime.utcnow()
        self.total_interactions += 1
    
    def add_agent(self, agent_role: AgentRole, agent: BaseAgent) -> None:
        """Add an agent to the constellation."""
        self.active_agents[agent_role] = agent
        if self.primary_agent is None:
            self.primary_agent = agent_role
    
    def get_primary_agent(self) -> Optional[BaseAgent]:
        """Get the primary agent."""
        if self.primary_agent and self.primary_agent in self.active_agents:
            return self.active_agents[self.primary_agent]
        return None
    
    def set_primary_agent(self, agent_role: AgentRole) -> bool:
        """Set the primary agent."""
        if agent_role in self.active_agents:
            self.primary_agent = agent_role
            return True
        return False
    
    def record_handoff(self) -> None:
        """Record an agent handoff."""
        self.handoff_count += 1
        self.update_activity()


class ConstellationManager:
    """
    Manages the formation and coordination of agent constellations.

    This class handles constellation creation, agent coordination,
    intelligent handoffs, and session management using LangGraph workflows.
    """

    def __init__(self, llm_client: Any = None):
        """Initialize constellation manager with LangGraph integration."""
        # Initialize LLM client if not provided
        if llm_client is None:
            from ..tools.llm_integration import LLMIntegration
            llm_integration = LLMIntegration()
            self.llm_client = llm_integration if llm_integration.is_available() else None
        else:
            self.llm_client = llm_client

        self.active_constellations: Dict[str, ActiveConstellation] = {}
        self.agent_factory = self._create_agent_factory()

        # LangGraph components
        self.workflow_graph: Optional[Any] = None
        self.tool_executor: Optional[Any] = None
        self.checkpointer: Optional[Any] = None

        # Initialize LangGraph workflow if available
        if LANGGRAPH_AVAILABLE:
            self._initialize_langgraph_workflow()

    def _initialize_langgraph_workflow(self) -> None:
        """Initialize LangGraph workflow for agent coordination."""
        try:
            # Initialize tool executor with LangChain tools
            langchain_tools = get_available_tools()
            self.tool_executor = ToolExecutor(langchain_tools)

            # Initialize checkpointer for state persistence
            self.checkpointer = SqliteSaver.from_conn_string(":memory:")

            # Create workflow graph
            workflow = StateGraph(ConstellationState)

            # Add nodes
            workflow.add_node("agent_selection", self._agent_selection_node)
            workflow.add_node("agent_execution", self._agent_execution_node)
            workflow.add_node("handoff_analysis", self._handoff_analysis_node)
            workflow.add_node("tool_execution", self._tool_execution_node)

            # Add edges
            workflow.set_entry_point("agent_selection")
            workflow.add_edge("agent_selection", "agent_execution")
            workflow.add_edge("agent_execution", "handoff_analysis")

            # Conditional edges for handoffs and tool usage
            workflow.add_conditional_edges(
                "handoff_analysis",
                self._should_handoff,
                {
                    "handoff": "agent_selection",
                    "tools": "tool_execution",
                    "end": END
                }
            )
            workflow.add_edge("tool_execution", "agent_execution")

            # Compile the graph
            self.workflow_graph = workflow.compile(checkpointer=self.checkpointer)

        except Exception as e:
            print(f"Warning: Failed to initialize LangGraph workflow: {e}")
            self.workflow_graph = None

    def _create_agent_factory(self) -> Dict[AgentRole, type]:
        """Create agent factory mapping."""
        return {
            # Knowledge Domain
            AgentRole.INSTRUCTOR: InstructorAgent,
            AgentRole.DOCUMENTATION_EXPERT: DocumentationExpertAgent,
            AgentRole.RESEARCH_ASSISTANT: ResearchAssistantAgent,
            AgentRole.KNOWLEDGE_SYNTHESIZER: KnowledgeSynthesizerAgent,
            
            # Practice Domain
            AgentRole.CODE_ASSISTANT: CodeAssistantAgent,
            AgentRole.PRACTICE_FACILITATOR: PracticeFacilitatorAgent,
            AgentRole.PROJECT_GUIDE: ProjectGuideAgent,
            AgentRole.TROUBLESHOOTER: TroubleshooterAgent,
            
            # Support Domain
            AgentRole.MENTOR: MentorAgent,
            AgentRole.MOTIVATIONAL_COACH: MotivationalCoachAgent,
            
            # Assessment Domain
            AgentRole.ASSESSMENT_AGENT: AssessmentAgent,
            AgentRole.PROGRESS_TRACKER: ProgressTrackerAgent,
        }
    
    async def create_constellation(
        self,
        constellation_type: ConstellationType,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_id: str,
    ) -> ActiveConstellation:
        """Create and initialize a new constellation."""
        
        # Get constellation configuration
        config = get_constellation_config(constellation_type)
        
        # Create constellation instance
        constellation = ActiveConstellation(
            constellation_id=f"{session_id}_{constellation_type.value}",
            constellation_type=constellation_type,
            session_id=session_id,
            user_id=user_profile.user_id,
            config=config,
            max_concurrent_agents=config.max_concurrent_agents,
        )
        
        # Initialize primary agents
        for agent_role in config.primary_agents:
            agent = await self._create_agent(agent_role)
            constellation.add_agent(agent_role, agent)
        
        # Initialize support agents if needed
        for agent_role in config.support_agents:
            if len(constellation.active_agents) < constellation.max_concurrent_agents:
                agent = await self._create_agent(agent_role)
                constellation.add_agent(agent_role, agent)
        
        # Set constellation status
        constellation.status = ConstellationStatus.ACTIVE
        
        # Store constellation
        self.active_constellations[constellation.constellation_id] = constellation
        
        return constellation
    
    async def _create_agent(self, agent_role: AgentRole) -> BaseAgent:
        """Create an agent instance."""
        agent_class = self.agent_factory.get(agent_role)
        if not agent_class:
            raise ValueError(f"Unknown agent role: {agent_role}")
        
        return agent_class(llm_client=self.llm_client)

    # LangGraph Node Functions
    async def _agent_selection_node(self, state: ConstellationState) -> ConstellationState:
        """Select the appropriate agent for the current context."""
        # Simple agent selection logic - in production this would be more sophisticated
        if not state.get("current_agent"):
            # Default to instructor for new conversations
            state["current_agent"] = "instructor"

        return state

    async def _agent_execution_node(self, state: ConstellationState) -> ConstellationState:
        """Execute the current agent to generate a response."""
        current_agent_role = state.get("current_agent", "instructor")

        # Find the agent in active constellation
        session_id = state.get("session_context", {}).get("session_id", "default")
        constellation = self._find_constellation_by_session(session_id)

        if constellation and current_agent_role in constellation.active_agents:
            agent = constellation.active_agents[current_agent_role]

            # Generate response using agent
            try:
                # Convert state to expected format
                user_profile = UserProfile(**state["user_profile"])
                framework = SupportedFrameworks(state["framework"])

                response = await agent.generate_response(
                    state["user_message"],
                    user_profile,
                    framework,
                    state["module_id"],
                    state["session_context"]
                )

                state["agent_response"] = response.content
                state["handoff_suggestion"] = response.handoff_suggestion.dict() if response.handoff_suggestion else None

            except Exception as e:
                state["agent_response"] = f"Error generating response: {str(e)}"
        else:
            state["agent_response"] = "Agent not available"

        state["iteration_count"] = state.get("iteration_count", 0) + 1
        return state

    async def _handoff_analysis_node(self, state: ConstellationState) -> ConstellationState:
        """Analyze if a handoff is needed."""
        # This node just passes through - the decision is made in _should_handoff
        return state

    async def _tool_execution_node(self, state: ConstellationState) -> ConstellationState:
        """Execute tools if needed."""
        if self.tool_executor and state.get("agent_response"):
            # Simple tool execution - in production this would be more sophisticated
            try:
                # Check if response suggests tool usage
                response_content = state["agent_response"]
                if "documentation" in response_content.lower():
                    # Use documentation tool
                    tool_result = await self.tool_executor.ainvoke({
                        "tool": "get_framework_documentation",
                        "tool_input": {
                            "framework": state["framework"],
                            "topic": state["user_message"][:50]
                        }
                    })
                    state["tools_used"] = state.get("tools_used", []) + ["get_framework_documentation"]

            except Exception as e:
                print(f"Tool execution error: {e}")

        return state

    def _should_handoff(self, state: ConstellationState) -> str:
        """Determine if handoff, tool usage, or end is needed."""
        # Check for handoff suggestion
        if state.get("handoff_suggestion"):
            return "handoff"

        # Check if tools should be used
        response = state.get("agent_response", "")
        if any(keyword in response.lower() for keyword in ["documentation", "example", "exercise"]):
            return "tools"

        # Check iteration limit
        if state.get("iteration_count", 0) >= 5:
            return "end"

        return "end"

    async def run_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
    ) -> AgentResponse:
        """Run a learning session with the active constellation using LangGraph workflow."""

        # Find active constellation for session
        constellation = self._find_constellation_by_session(session_id)
        if not constellation:
            # Create default constellation if none exists
            optimal_types = get_optimal_constellation_for_profile(
                user_profile.preferred_learning_style.value,
                user_profile.python_skill_level.value,
                user_profile.learning_pace.value
            )
            constellation_type = optimal_types[0] if optimal_types else ConstellationType.THEORY_PRACTICE_BALANCED
            constellation = await self.create_constellation(
                constellation_type, user_profile, framework, module_id, session_id
            )

        # Use LangGraph workflow if available
        if self.workflow_graph and LANGGRAPH_AVAILABLE:
            return await self._run_session_with_langgraph(
                session_id, user_message, user_profile, framework, module_id, constellation
            )
        else:
            # Fallback to original implementation
            return await self._run_session_fallback(
                session_id, user_message, user_profile, framework, module_id, constellation
            )

    async def _run_session_with_langgraph(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        constellation: ActiveConstellation,
    ) -> AgentResponse:
        """Run session using LangGraph workflow."""

        # Prepare initial state
        initial_state: ConstellationState = {
            "user_message": user_message,
            "user_profile": user_profile.dict(),
            "framework": framework.value,
            "module_id": module_id,
            "session_context": {"session_id": session_id, **constellation.context_data},
            "current_agent": constellation.primary_agent.value if constellation.primary_agent else "instructor",
            "agent_response": None,
            "handoff_suggestion": None,
            "conversation_history": constellation.conversation_history,
            "tools_used": [],
            "iteration_count": 0,
        }

        # Execute workflow
        config = {"configurable": {"thread_id": session_id}}
        final_state = await self.workflow_graph.ainvoke(initial_state, config)

        # Create response from final state
        response = AgentResponse(
            agent_role=AgentRole(final_state["current_agent"]),
            content=final_state.get("agent_response", "No response generated"),
            response_type="workflow",
            handoff_suggestion=None,  # Handoffs are handled within workflow
            learning_objectives_addressed=["workflow_execution"],
            difficulty_level="intermediate",
            estimated_reading_time_minutes=2.0,
            includes_code_example="```" in final_state.get("agent_response", ""),
            includes_exercise="exercise" in final_state.get("agent_response", "").lower(),
            includes_links="http" in final_state.get("agent_response", ""),
            processing_time_seconds=0.0,
        )

        # Update constellation state
        constellation.update_activity()
        constellation.conversation_history.append({
            "user_message": user_message,
            "agent_response": response.dict(),
            "timestamp": datetime.utcnow().isoformat(),
            "tools_used": final_state.get("tools_used", []),
        })

        return response

    async def _run_session_fallback(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        constellation: ActiveConstellation,
    ) -> AgentResponse:
        """Fallback session execution without LangGraph."""

        # Get primary agent
        primary_agent = constellation.get_primary_agent()
        if not primary_agent:
            raise RuntimeError("No primary agent available in constellation")

        # Generate response
        response = await primary_agent.generate_response(
            user_message, user_profile, framework, module_id, constellation.context_data
        )

        # Update constellation state
        constellation.update_activity()
        constellation.conversation_history.append({
            "user_message": user_message,
            "agent_response": response.dict(),
            "timestamp": datetime.utcnow().isoformat(),
        })

        # Handle potential handoffs
        if response.handoff_suggestion:
            await self._handle_handoff(constellation, response.handoff_suggestion)

        return response
    
    async def _handle_handoff(
        self,
        constellation: ActiveConstellation,
        handoff_suggestion: HandoffSuggestion,
    ) -> bool:
        """Handle agent handoff within constellation."""
        
        target_role = handoff_suggestion.target_agent
        
        # Check if target agent is already active
        if target_role not in constellation.active_agents:
            # Check if we can add more agents
            if len(constellation.active_agents) >= constellation.max_concurrent_agents:
                # Remove least recently used agent (simple strategy)
                # In production, this would be more sophisticated
                pass
            
            # Create and add target agent
            try:
                target_agent = await self._create_agent(target_role)
                constellation.add_agent(target_role, target_agent)
            except Exception as e:
                # Log error and continue with current agent
                return False
        
        # Set new primary agent
        success = constellation.set_primary_agent(target_role)
        if success:
            constellation.record_handoff()
            
            # Update context with handoff data
            constellation.context_data.update(handoff_suggestion.context_data)
        
        return success
    
    def _find_constellation_by_session(self, session_id: str) -> Optional[ActiveConstellation]:
        """Find constellation by session ID."""
        for constellation in self.active_constellations.values():
            if constellation.session_id == session_id:
                return constellation
        return None
    
    async def get_optimal_constellation_type(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> ConstellationType:
        """Determine optimal constellation type for user and context."""
        
        # Get profile-based recommendations
        optimal_types = get_optimal_constellation_for_profile(
            user_profile.preferred_learning_style.value,
            user_profile.python_skill_level.value,
            user_profile.learning_pace.value
        )
        
        # Apply contextual adjustments
        if session_context.get("needs_debugging"):
            return ConstellationType.PROJECT_ORIENTED
        elif session_context.get("assessment_requested"):
            return ConstellationType.ASSESSMENT_FOCUSED
        elif session_context.get("research_intensive"):
            return ConstellationType.RESEARCH_INTENSIVE
        
        # Return best match or default
        return optimal_types[0] if optimal_types else ConstellationType.THEORY_PRACTICE_BALANCED
    
    def get_constellation_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status information for a constellation."""
        constellation = self._find_constellation_by_session(session_id)
        if not constellation:
            return None
        
        return {
            "constellation_id": constellation.constellation_id,
            "constellation_type": constellation.constellation_type,
            "status": constellation.status,
            "primary_agent": constellation.primary_agent,
            "active_agents": list(constellation.active_agents.keys()),
            "total_interactions": constellation.total_interactions,
            "handoff_count": constellation.handoff_count,
            "created_at": constellation.created_at.isoformat(),
            "last_activity_at": constellation.last_activity_at.isoformat(),
        }
    
    async def cleanup_inactive_constellations(self, max_age_hours: int = 24) -> int:
        """Clean up inactive constellations."""
        current_time = datetime.utcnow()
        cleanup_count = 0
        
        constellations_to_remove = []
        for constellation_id, constellation in self.active_constellations.items():
            age_hours = (current_time - constellation.last_activity_at).total_seconds() / 3600
            if age_hours > max_age_hours:
                constellations_to_remove.append(constellation_id)
        
        for constellation_id in constellations_to_remove:
            del self.active_constellations[constellation_id]
            cleanup_count += 1
        
        return cleanup_count
