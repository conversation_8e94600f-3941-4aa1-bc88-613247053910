#!/usr/bin/env python3
"""
GAAPF API Server Launcher

This script launches the FastAPI server for GAAPF.
"""

import sys
import os
from pathlib import Path

def main():
    """Launch the FastAPI server."""
    
    print("🔌 Starting GAAPF API Server...")
    print("=" * 50)
    
    # Add src to Python path
    src_path = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_path))
    
    # Set environment variables
    os.environ.setdefault("API_HOST", "0.0.0.0")
    os.environ.setdefault("API_PORT", "8000")
    
    try:
        # Import and run the API
        from pyframeworks_assistant.interfaces.api.main import run_api
        
        host = os.getenv("API_HOST", "0.0.0.0")
        port = int(os.getenv("API_PORT", "8000"))
        
        print(f"🚀 Starting API server on {host}:{port}")
        print(f"📚 API Documentation: http://{host}:{port}/docs")
        print(f"📖 ReDoc Documentation: http://{host}:{port}/redoc")
        print()
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        run_api(host=host, port=port, reload=True)
        
    except KeyboardInterrupt:
        print("\n\n👋 API server stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting API server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
