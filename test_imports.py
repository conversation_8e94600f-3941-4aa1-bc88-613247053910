#!/usr/bin/env python3
"""
Simple import test for GAAPF system.
"""

import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    print("Testing basic imports...")
    
    # Test tools import
    from pyframeworks_assistant.tools import LLMIntegration, LLMProvider, GoogleAIService
    print("✅ Tools import successful")
    
    # Test config imports
    from pyframeworks_assistant.config.user_profiles import UserProfile, SkillLevel
    print("✅ Config import successful")
    
    # Test agent imports
    from pyframeworks_assistant.agents.base_agent import BaseAgent
    print("✅ Agent import successful")
    
    # Test core imports
    from pyframeworks_assistant.core.constellation import ConstellationManager
    print("✅ Core import successful")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
