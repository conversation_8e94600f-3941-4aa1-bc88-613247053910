#!/usr/bin/env python3
"""
Simple tools test for GAAPF system.
"""

import sys
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    print("Testing tools...")
    
    # Test tools import
    from pyframeworks_assistant.tools import AVAILABLE_TOOLS, get_available_tools
    print(f"✅ Found {len(AVAILABLE_TOOLS)} tools")
    
    # Test tool names
    tool_names = []
    for tool in AVAILABLE_TOOLS:
        if hasattr(tool, 'name'):
            tool_names.append(tool.name)
        elif hasattr(tool, '__name__'):
            tool_names.append(tool.__name__)
        else:
            tool_names.append(str(tool))
    
    print(f"Tool names: {tool_names}")
    
    # Test LangChain tools function
    langchain_tools = get_available_tools()
    print(f"✅ LangChain tools function returned {len(langchain_tools)} tools")
    
    # Test individual tools
    from pyframeworks_assistant.tools import get_framework_documentation
    result = get_framework_documentation("langchain", "agents")
    print(f"✅ Documentation tool works: {len(result)} characters")
    
    print("\n🎉 All tool tests passed!")
    
except Exception as e:
    print(f"❌ Tool test failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
