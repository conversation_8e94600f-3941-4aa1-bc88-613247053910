#!/usr/bin/env python3
"""
GAAPF Installation Script

This script helps set up GAAPF with proper dependencies and configuration.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """Print installation header."""
    print("🤖 GAAPF - Guidance AI Agent for Python Framework")
    print("=" * 60)
    print("Installation and Setup Script")
    print()

def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 10):
        print("❌ Error: Python 3.10 or higher is required")
        print(f"   Current version: {sys.version}")
        print("   Please upgrade Python and try again.")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} - Compatible")
    return True

def check_git():
    """Check if git is available."""
    print("📦 Checking Git availability...")
    
    try:
        subprocess.run(["git", "--version"], capture_output=True, check=True)
        print("✅ Git is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  Git not found - some features may be limited")
        return False

def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
        
        # Install requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        print("   Please check your internet connection and try again.")
        return False

def setup_environment():
    """Set up environment configuration."""
    print("⚙️  Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from template")
        print("   Please edit .env with your API keys")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example found - creating basic .env")
        with open(env_file, 'w') as f:
            f.write("# GAAPF Configuration\n")
            f.write("# Add your API keys here\n")
            f.write("GOOGLE_API_KEY=your_google_gemini_api_key_here\n")
            f.write("OPENAI_API_KEY=your_openai_api_key_here\n")
            f.write("ANTHROPIC_API_KEY=your_anthropic_api_key_here\n")
    
    return True

def create_data_directories():
    """Create necessary data directories."""
    print("📁 Creating data directories...")
    
    directories = [
        "data",
        "data/user_profiles",
        "data/temporal_state",
        "data/analytics",
        "data/sessions"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Data directories created")
    return True

def test_installation():
    """Test the installation."""
    print("🧪 Testing installation...")
    
    try:
        # Run basic tests
        result = subprocess.run([sys.executable, "test_basic.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Installation test passed")
            return True
        else:
            print("❌ Installation test failed")
            print("Error output:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  Test timed out - installation may still be working")
        return True
    except Exception as e:
        print(f"⚠️  Could not run tests: {e}")
        return True

def print_next_steps():
    """Print next steps for the user."""
    print()
    print("🎉 Installation Complete!")
    print("=" * 60)
    print()
    print("Next Steps:")
    print("1. 🔑 Edit .env file with your API keys:")
    print("   - Google Gemini (free): https://makersuite.google.com/app/apikey")
    print("   - OpenAI: https://platform.openai.com/api-keys")
    print("   - Anthropic: https://console.anthropic.com/")
    print()
    print("2. 🚀 Run GAAPF:")
    print("   CLI (recommended):  python run_cli.py")
    print("   Web demo:          streamlit run src/pyframeworks_assistant/interfaces/web/streamlit_app.py")
    print("   API server:        python -m uvicorn src.pyframeworks_assistant.interfaces.api.main:app --reload")
    print()
    print("3. 📚 Read the documentation:")
    print("   CLI Guide:         CLI_GUIDE.md")
    print("   API Docs:          API_DOCUMENTATION.md")
    print("   Methodology:       GAAPF_Methodology_Report.md")
    print()
    print("Happy learning with GAAPF! 🤖📚")

def main():
    """Main installation function."""
    print_header()
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    check_git()
    
    # Install and setup
    steps = [
        ("Installing dependencies", install_dependencies),
        ("Setting up environment", setup_environment),
        ("Creating data directories", create_data_directories),
        ("Testing installation", test_installation),
    ]
    
    for step_name, step_func in steps:
        print()
        if not step_func():
            print(f"❌ Failed: {step_name}")
            print("Installation incomplete. Please check errors above.")
            sys.exit(1)
    
    print_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Installation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error during installation: {e}")
        sys.exit(1)
