"""
Base agent implementation for the GAAPF constellation system.

This module provides the foundational BaseAgent class that all specialized
agents inherit from, along with common response and handoff structures.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import AgentRole


class HandoffConfidence(str, Enum):
    """Confidence levels for agent handoffs."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class HandoffSuggestion(BaseModel):
    """Suggestion for handing off to another agent."""
    
    target_agent: AgentRole = Field(..., description="Suggested target agent")
    confidence: HandoffConfidence = Field(..., description="Confidence in handoff suggestion")
    reason: str = Field(..., description="Reason for handoff suggestion")
    context_data: Dict[str, Any] = Field(default_factory=dict, description="Context to pass to next agent")
    
    class Config:
        use_enum_values = True


class AgentResponse(BaseModel):
    """Response from an agent including content and metadata."""
    
    agent_role: AgentRole = Field(..., description="Role of responding agent")
    content: str = Field(..., description="Response content")
    response_type: str = Field("text", description="Type of response")
    
    # Handoff information
    handoff_suggestion: Optional[HandoffSuggestion] = Field(None, description="Handoff suggestion")
    
    # Learning metadata
    learning_objectives_addressed: List[str] = Field(default_factory=list)
    difficulty_level: str = Field("intermediate", description="Content difficulty level")
    estimated_reading_time_minutes: float = Field(2.0, description="Estimated reading time")
    
    # Engagement data
    includes_code_example: bool = Field(False, description="Whether response includes code")
    includes_exercise: bool = Field(False, description="Whether response includes exercise")
    includes_links: bool = Field(False, description="Whether response includes external links")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_time_seconds: float = Field(0.0, description="Time taken to generate response")
    
    class Config:
        use_enum_values = True


class BaseAgent(ABC):
    """
    Base class for all GAAPF agents.
    
    Provides common functionality for agent coordination, handoff logic,
    and response generation while allowing specialized implementations.
    """
    
    def __init__(
        self,
        agent_role: AgentRole,
        llm_client: Any,
        system_prompt: str = "",
        **kwargs
    ):
        """Initialize base agent."""
        self.agent_role = agent_role
        self.llm_client = llm_client
        self.system_prompt = system_prompt
        self.conversation_history: List[Dict[str, Any]] = []
        self.context_data: Dict[str, Any] = {}
        
        # Agent-specific configuration
        self.max_response_length = kwargs.get("max_response_length", 2000)
        self.handoff_keywords = kwargs.get("handoff_keywords", [])
        self.specialization_areas = kwargs.get("specialization_areas", [])
    
    @abstractmethod
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate a response to user input."""
        pass
    
    @abstractmethod
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze if a handoff to another agent would be beneficial."""
        pass
    
    def update_context(self, key: str, value: Any) -> None:
        """Update agent context data."""
        self.context_data[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get value from agent context."""
        return self.context_data.get(key, default)
    
    def add_to_history(self, role: str, content: str) -> None:
        """Add message to conversation history."""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.utcnow().isoformat(),
        })
    
    def get_recent_history(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        return self.conversation_history[-limit:] if self.conversation_history else []
    
    def clear_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
    
    def _detect_handoff_keywords(self, text: str) -> List[str]:
        """Detect handoff trigger keywords in text."""
        text_lower = text.lower()
        detected = []
        
        for keyword in self.handoff_keywords:
            if keyword.lower() in text_lower:
                detected.append(keyword)
        
        return detected
    
    def _calculate_handoff_confidence(
        self,
        keyword_matches: List[str],
        context_relevance: float,
        agent_capability_match: float,
    ) -> HandoffConfidence:
        """Calculate confidence level for handoff suggestion."""
        # Simple scoring algorithm
        score = 0.0
        
        # Keyword matching (0-40 points)
        score += min(len(keyword_matches) * 10, 40)
        
        # Context relevance (0-30 points)
        score += context_relevance * 30
        
        # Agent capability match (0-30 points)
        score += agent_capability_match * 30
        
        if score >= 70:
            return HandoffConfidence.CRITICAL
        elif score >= 50:
            return HandoffConfidence.HIGH
        elif score >= 30:
            return HandoffConfidence.MEDIUM
        else:
            return HandoffConfidence.LOW
    
    def _build_system_prompt(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
    ) -> str:
        """Build system prompt with context."""
        base_prompt = self.system_prompt
        
        # Add user context
        context_additions = f"""

User Context:
- Skill Level: {user_profile.python_skill_level}
- Learning Style: {user_profile.preferred_learning_style}
- Learning Pace: {user_profile.learning_pace}
- Experience: {user_profile.programming_experience_years} years
- Framework: {framework}
- Current Module: {module_id}

Adapt your responses to match the user's skill level and learning preferences.
"""
        
        return base_prompt + context_additions
    
    async def _call_llm(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
    ) -> str:
        """Call the LLM with error handling."""
        try:
            if self.llm_client:
                # Use actual LLM client if available
                from ..tools.llm_integration import LLMIntegration
                if hasattr(self.llm_client, 'generate_response'):
                    return await self.llm_client.generate_response(messages)
                else:
                    # Fallback for direct LangChain clients
                    from langchain_core.messages import HumanMessage, SystemMessage
                    langchain_messages = []
                    for msg in messages:
                        if msg["role"] == "system":
                            langchain_messages.append(SystemMessage(content=msg["content"]))
                        elif msg["role"] == "user":
                            langchain_messages.append(HumanMessage(content=msg["content"]))

                    response = await self.llm_client.ainvoke(langchain_messages)
                    return response.content
            else:
                # Return enhanced mock response
                return self._generate_enhanced_mock_response(messages)
        except Exception as e:
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"

    def _generate_enhanced_mock_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate enhanced mock response based on agent role and message content."""
        last_message = messages[-1].get('content', '') if messages else ''
        agent_name = self.agent_role.value.replace('_', ' ').title()

        # Agent-specific mock responses
        if self.agent_role.value == "instructor":
            return self._mock_instructor_response(last_message)
        elif self.agent_role.value == "code_assistant":
            return self._mock_code_response(last_message)
        elif self.agent_role.value == "practice_facilitator":
            return self._mock_practice_response(last_message)
        else:
            return f"[{agent_name}] I understand you're asking about: '{last_message[:100]}...'\n\nThis is a mock response. In the full system with LLM integration, I would provide detailed, personalized assistance based on your question and learning profile."

    def _mock_instructor_response(self, message: str) -> str:
        """Mock instructor response."""
        if "langchain" in message.lower():
            return """LangChain is a powerful framework for building applications with Large Language Models (LLMs). Let me break this down for you:

## Core Components:
1. **LLMs and Chat Models** - Interface with language models like GPT, Claude, etc.
2. **Prompts and Templates** - Structure your inputs for consistent results
3. **Chains** - Connect multiple components together
4. **Agents** - Let LLMs make decisions and use tools
5. **Memory** - Maintain conversation state and context

## Why LangChain?
- **Abstraction**: Unified interface for different LLM providers
- **Modularity**: Mix and match components as needed
- **Production-ready**: Built for real applications, not just experiments

Would you like me to explain any of these components in more detail, or shall we look at some practical examples?"""

        elif "langgraph" in message.lower():
            return """LangGraph is an advanced library for building stateful, multi-actor applications with LLMs. Here's what makes it special:

## Key Concepts:
1. **State Graphs** - Define your application as a graph of states and transitions
2. **Nodes** - Individual processing steps (agents, tools, or functions)
3. **Edges** - Connections that define the flow between nodes
4. **Conditional Routing** - Dynamic decision-making in your workflow
5. **Persistence** - Save and resume complex workflows

## When to Use LangGraph:
- **Multi-agent systems** with complex interactions
- **Workflows** that need state management
- **Applications** requiring human-in-the-loop patterns
- **Complex routing** based on content or conditions

LangGraph builds on LangChain but adds powerful orchestration capabilities. Ready to dive deeper into any specific aspect?"""

        else:
            return f"""Great question! As your instructor, I'm here to help you understand complex concepts step by step.

Based on your question about "{message[:50]}...", let me provide a structured explanation:

## Understanding the Concept
This topic involves several key aspects that we should explore together.

## Learning Approach
1. **Start with fundamentals** - Build a solid foundation
2. **See practical examples** - Connect theory to real applications
3. **Practice hands-on** - Reinforce learning through doing
4. **Ask questions** - Clarify any confusing points

What specific aspect would you like me to explain first? I can provide more detailed explanations, show examples, or create practice exercises based on your preference."""

    def _mock_code_response(self, message: str) -> str:
        """Mock code assistant response."""
        if "example" in message.lower() or "code" in message.lower():
            return """Here's a practical code example to illustrate the concept:

```python
# Basic LangChain example
from langchain.llms import OpenAI
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate

# Initialize the LLM
llm = OpenAI(temperature=0.7)

# Create a prompt template
prompt = PromptTemplate(
    input_variables=["topic"],
    template="Explain {topic} in simple terms with an example"
)

# Create a chain
chain = LLMChain(llm=llm, prompt=prompt)

# Use the chain
result = chain.run(topic="machine learning")
print(result)
```

## Key Points:
- **LLM**: The language model that generates responses
- **PromptTemplate**: Structures your input consistently
- **LLMChain**: Combines the LLM and prompt for easy use
- **Variables**: Make your prompts dynamic and reusable

Try running this code and experiment with different topics! What would you like to explore next?"""

        else:
            return f"""Let me help you with the code implementation for "{message[:50]}...":

```python
# Code example related to your question
def example_function():
    \"\"\"
    This function demonstrates the concept you're asking about.
    In a real implementation, this would be tailored to your specific question.
    \"\"\"
    print("This is a mock code example")
    return "result"

# Usage example
result = example_function()
print(f"Result: {result}")
```

## Next Steps:
1. **Try the code** - Run it in your environment
2. **Modify it** - Experiment with different parameters
3. **Ask questions** - If anything is unclear
4. **Build on it** - Use this as a foundation for more complex examples

What specific aspect of the implementation would you like me to explain or expand on?"""

    def _mock_practice_response(self, message: str) -> str:
        """Mock practice facilitator response."""
        return f"""Excellent! Let's create some hands-on practice for "{message[:50]}...":

## 🎯 Practice Exercise

**Your Task:**
Build a simple application that demonstrates the concept we've been discussing.

**Step-by-Step Guide:**
1. **Setup** - Create a new Python file
2. **Import** - Add the necessary imports
3. **Implement** - Write the core functionality
4. **Test** - Run and verify your implementation
5. **Experiment** - Try different variations

## 💡 Practice Tips:
- Start simple and gradually add complexity
- Test each step before moving to the next
- Don't hesitate to ask for help if you get stuck
- Try to break things - it's a great way to learn!

## 🏆 Challenge:
Once you complete the basic exercise, try to:
- Add error handling
- Improve the user interface
- Extend functionality with new features

Ready to start? Let me know if you need any clarification on the exercise or want me to break it down into smaller steps!"""
    
    def get_agent_description(self) -> str:
        """Get a description of this agent's capabilities."""
        descriptions = {
            AgentRole.INSTRUCTOR: "I provide clear explanations and structured learning guidance.",
            AgentRole.CODE_ASSISTANT: "I help with code examples, debugging, and implementation.",
            AgentRole.DOCUMENTATION_EXPERT: "I provide detailed documentation and reference information.",
            AgentRole.RESEARCH_ASSISTANT: "I explore advanced topics and cutting-edge features.",
            AgentRole.PRACTICE_FACILITATOR: "I create exercises and hands-on learning activities.",
            AgentRole.PROJECT_GUIDE: "I guide you through building real-world projects.",
            AgentRole.MENTOR: "I provide personalized guidance and learning strategy advice.",
            AgentRole.ASSESSMENT_AGENT: "I evaluate your progress and provide assessments.",
            AgentRole.PROGRESS_TRACKER: "I track your learning progress and milestones.",
            AgentRole.MOTIVATIONAL_COACH: "I provide encouragement and motivation.",
            AgentRole.TROUBLESHOOTER: "I help debug issues and solve technical problems.",
            AgentRole.KNOWLEDGE_SYNTHESIZER: "I synthesize information from multiple sources.",
        }
        return descriptions.get(self.agent_role, "I'm here to help with your learning journey.")
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.agent_role.value.title().replace('_', ' ')} Agent"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"<{self.__class__.__name__}(role={self.agent_role.value})>"
