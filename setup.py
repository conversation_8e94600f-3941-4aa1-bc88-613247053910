#!/usr/bin/env python3
"""
Setup script for GAAPF - Guidance AI Agent for Python Framework
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements = []
with open('requirements.txt', 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#'):
            # Remove version constraints for setup.py
            package = line.split('>=')[0].split('==')[0].split('<')[0]
            requirements.append(package)

setup(
    name="gaapf-guidance-ai-agent",
    version="1.0.0",
    author="GAAPF Team",
    author_email="<EMAIL>",
    description="An Adaptive Multi-Agent Learning System for AI Framework Education",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/gaapf-guidance-ai-agent",
    project_urls={
        "Bug Tracker": "https://github.com/your-username/gaapf-guidance-ai-agent/issues",
        "Documentation": "https://github.com/your-username/gaapf-guidance-ai-agent/blob/main/README.md",
        "Source Code": "https://github.com/your-username/gaapf-guidance-ai-agent",
    },
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Education",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=8.0.0",
            "pytest-asyncio>=0.24.0",
            "black>=24.0.0",
            "ruff>=0.8.0",
            "mypy>=1.13.0",
        ],
        "full": [
            "redis>=5.0.0",
            "psycopg2-binary>=2.9.0",
            "sqlalchemy>=2.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "gaapf=pyframeworks_assistant.interfaces.cli.cli_app:main",
            "gaapf-api=pyframeworks_assistant.interfaces.api.main:run_api",
        ],
    },
    include_package_data=True,
    package_data={
        "pyframeworks_assistant": [
            "data/**/*",
            "config/**/*",
        ],
    },
    keywords=[
        "ai", "education", "langchain", "langgraph", "multi-agent", 
        "adaptive-learning", "personalized-learning", "llm", "chatbot"
    ],
    zip_safe=False,
)
